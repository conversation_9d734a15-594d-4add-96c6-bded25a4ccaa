import type { SVGProps } from "react";

export const PictureLandscapeIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={48}
    height={48}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      fill="#B2B2B2"
      d="M13.587 2.826H2.427A1.594 1.594 0 0 0 .834 4.42v7.174a1.594 1.594 0 0 0 1.595 1.595h11.16a1.597 1.597 0 0 0 1.594-1.595V4.42a1.594 1.594 0 0 0-1.595-1.594Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.182 5.217V4.42a1.594 1.594 0 0 0-1.595-1.594H2.427A1.594 1.594 0 0 0 .834 4.42v7.174a1.594 1.594 0 0 0 1.595 1.595h.797"
    />
    <path
      fill="#E3E3E3"
      d="M17.572 6.812H6.412a1.594 1.594 0 0 0-1.594 1.594v7.174a1.594 1.594 0 0 0 1.595 1.594h11.16a1.594 1.594 0 0 0 1.594-1.594V8.406a1.594 1.594 0 0 0-1.595-1.594Z"
    />
    <path
      fill="#fff"
      d="M6.413 6.812a1.594 1.594 0 0 0-1.595 1.594v7.174a1.594 1.594 0 0 0 1.324 1.567L16.478 6.812H6.413Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M17.572 6.812H6.412a1.594 1.594 0 0 0-1.594 1.594v7.174a1.594 1.594 0 0 0 1.595 1.594h11.16a1.594 1.594 0 0 0 1.594-1.594V8.406a1.594 1.594 0 0 0-1.595-1.594Z"
    />
    <path
      fill="#78EB7B"
      d="m7.21 14.783 2.611-5.224a.646.646 0 0 1 .577-.356.707.707 0 0 1 .606.341l2.2 3.644.94-1.275a.79.79 0 0 1 .639-.319.646.646 0 0 1 .577.357l1.415 2.832H7.21Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m7.21 14.783 2.611-5.224a.646.646 0 0 1 .577-.356.707.707 0 0 1 .606.341l2.2 3.644.94-1.275a.79.79 0 0 1 .639-.319.646.646 0 0 1 .577.357l1.415 2.832H7.21Z"
    />
  </svg>
)

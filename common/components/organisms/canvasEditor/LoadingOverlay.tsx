'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { Loader2, Wand2, Save, Sparkles, Shapes } from 'lucide-react';

interface LoadingOverlayProps {
  isVisible: boolean;
  type: 'generating' | 'saving' | 'vectorizing' | 'improving';
  message?: string;
}

const getLoadingConfig = (type: LoadingOverlayProps['type']) => {
  switch (type) {
    case 'generating':
      return {
        icon: Wand2,
        title: 'Generating Image',
        defaultMessage: 'Creating your image with AI...',
        gradient: 'from-han-purple to-tulip',
      };
    case 'saving':
      return {
        icon: Save,
        title: 'Saving to Post',
        defaultMessage: 'Saving your design...',
        gradient: 'from-emerald-500 to-teal-500',
      };
    case 'vectorizing':
      return {
        icon: Shapes,
        title: 'Creating Vector',
        defaultMessage: 'Generating vector image...',
        gradient: 'from-violet-500 to-purple-500',
      };
    case 'improving':
      return {
        icon: Sparkles,
        title: 'Improving Image',
        defaultMessage: 'Enhancing your image with AI...',
        gradient: 'from-amber-500 to-orange-500',
      };
    default:
      return {
        icon: Loader2,
        title: 'Processing',
        defaultMessage: 'Please wait...',
        gradient: 'from-gray-500 to-gray-600',
      };
  }
};

export const LoadingOverlay = ({ isVisible, type, message }: LoadingOverlayProps) => {
  const config = getLoadingConfig(type);
  const Icon = config.icon;

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[100] flex items-center justify-center"
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="bg-neutral-900/95 backdrop-blur-md rounded-3xl p-8 text-center max-w-sm mx-4 border border-neutral-700/50 shadow-2xl"
      >
        {/* Animated Icon */}
        <motion.div
          animate={{ 
            rotate: type === 'generating' || type === 'improving' || type === 'vectorizing' ? 360 : 0,
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            rotate: { duration: 2, repeat: Infinity, ease: "linear" },
            scale: { duration: 1.5, repeat: Infinity, ease: "easeInOut" }
          }}
          className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br ${config.gradient} flex items-center justify-center shadow-lg`}
        >
          <Icon className="w-8 h-8 text-white" />
        </motion.div>

        {/* Title */}
        <h3 className="text-white font-semibold text-xl mb-2">
          {config.title}
        </h3>

        {/* Message */}
        <p className="text-gray-400 text-sm mb-6">
          {message || config.defaultMessage}
        </p>

        {/* Loading Animation */}
        <div className="flex justify-center space-x-1">
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: index * 0.2,
                ease: "easeInOut",
              }}
              className={`w-2 h-2 rounded-full bg-gradient-to-r ${config.gradient}`}
            />
          ))}
        </div>

        {/* Progress Bar */}
        <div className="mt-6 w-full bg-neutral-800 rounded-full h-1 overflow-hidden">
          <motion.div
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className={`h-full bg-gradient-to-r ${config.gradient} rounded-full`}
          />
        </div>
      </motion.div>
    </motion.div>
  );
};
